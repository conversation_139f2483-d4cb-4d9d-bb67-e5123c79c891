# Enhanced <PERSON>wen Coder Agent

A professional-grade AI-powered coding assistant built with Qwen models, designed to replace traditional development environments with intelligent automation. Inspired by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and other advanced coding assistants.

## 🚀 Key Features

### 🧠 **Intelligent Code Generation & Editing**
- Advanced code generation in 20+ programming languages
- Context-aware code suggestions and completions
- Intelligent file editing with diff preview
- Automatic code formatting and optimization

### 🖥️ **Enhanced Terminal Integration**
- **Cross-platform compatibility** (Windows, Linux, macOS)
- **Real-time streaming output** for long-running commands
- **Safe command detection** with automatic validation
- **Interactive command execution** with confirmation prompts

### 💾 **Professional Session Management**
- **Persistent sessions** with automatic save/restore
- **Conversation history** tracking across sessions
- **Active file tracking** and context maintenance
- **Project state persistence** between sessions

### 🔧 **Advanced Configuration System**
- **Professional configuration management** with AgentConfig
- **Multiple AI provider support** (ready for OpenAI, Claude, etc.)
- **Customizable behavior** and safety settings
- **Environment-specific configurations**

### 🛡️ **Enterprise-Grade Safety**
- **Safe mode** with command validation
- **Automatic file backups** before modifications
- **Transaction-based operations** with rollback capability
- **Permission-based access control**

### 📊 **Project Intelligence**
- **Deep project analysis** and structure understanding
- **Git integration** with branch and status tracking
- **Dependency detection** and management
- **Code quality assessment**

## 🏗️ Architecture

The Enhanced Qwen Coder Agent features a modern, modular architecture:

- **AgentConfig**: Professional configuration management
- **SessionData**: Persistent session state management  
- **Enhanced Terminal**: Cross-platform command execution with streaming
- **File Operations**: Atomic file operations with backup/restore
- **AI Integration**: Multi-provider AI routing and fallback

## 📦 Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd qwen-coder-agent
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt

# Optional: Set up Hugging Face API key for full AI features
# Get your free token at: https://huggingface.co/settings/tokens
set HF_TOKEN=your_token_here
```

3. **Set up your API token:**
```bash
export HF_TOKEN="your_huggingface_token_here"
```

## 🎯 Usage

### Interactive Mode (Recommended)

Start the enhanced agent:

**With Full AI Features (Recommended):**
```bash
# Set your Hugging Face API token
set HF_TOKEN=your_token_here
python qwen_agent.py
```

**Demo Mode (File Operations + Terminal):**
```bash
python qwen_agent.py --api-key demo
```

The agent will automatically:
- ✅ Initialize with enhanced configuration
- ✅ Load your previous session
- ✅ Analyze the current project
- ✅ Display git status and project info
- ✅ Enable streaming command execution
- ✅ Provide intelligent AI responses (with API key)
- ✅ Full file and terminal operations (always available)

### Direct Commands

```bash
# Edit files with AI assistance
python qwen_agent.py edit main.py "Add comprehensive error handling"

# Execute commands with streaming output
python qwen_agent.py run "npm install --verbose"

# Analyze project with deep insights
python qwen_agent.py analyze

# Start enhanced chat mode
python qwen_agent.py chat
```

## 🎮 Enhanced Commands

| Command | Description | Enhanced Features |
|---------|-------------|-------------------|
| `help` | Show all commands | Interactive help with examples |
| `edit <file>` | AI-powered file editing | Diff preview, backup, validation |
| `create <file>` | Create files with AI | Template detection, best practices |
| `run <command>` | Execute terminal commands | Streaming output, safety validation |
| `analyze` | Deep project analysis | Git status, dependencies, structure |
| `chat` | Interactive AI chat | Context-aware, session persistence |
| `files` | Enhanced file listing | Smart categorization, active tracking |
| `config` | Configuration management | Professional settings, validation |
| `backup` | Backup management | Automatic, versioned, restorable |
| `session` | Session management | Save, restore, history |

## ⚙️ Configuration

The agent uses a professional configuration system with `AgentConfig`:

```bash
# View current configuration
python qwen_agent.py config

# Set configuration values
python qwen_agent.py config set model "Qwen/Qwen2.5-Coder-32B-Instruct"
python qwen_agent.py config set temperature 0.7
python qwen_agent.py config set streaming true
python qwen_agent.py config set safe_mode true
python qwen_agent.py config set auto_save true
```

### Configuration Options

- **api_key**: Your Hugging Face API token
- **model**: AI model to use
- **max_tokens**: Maximum response length
- **temperature**: Response creativity (0.0-1.0)
- **streaming**: Enable real-time output streaming
- **safe_mode**: Enable command safety validation
- **auto_save**: Automatic session saving
- **backup_enabled**: Enable automatic backups
- **git_integration**: Enable git status tracking

## 🛡️ Safety & Security

### Safe Command Detection
The agent automatically validates commands and warns about potentially dangerous operations:

```bash
✅ Safe: ls, pwd, python --version, npm install
⚠️  Requires confirmation: rm, del, sudo, format
❌ Blocked in safe mode: rm -rf /, format c:
```

### Automatic Backups
All file modifications are automatically backed up:
- **Location**: `.qwen_backups/` directory
- **Format**: Timestamped backup files
- **Restoration**: Automatic rollback on errors

### Session Security
- **Sensitive data**: Commands not persisted for security
- **Session isolation**: Each session has unique ID
- **Safe storage**: Only conversation history saved

## 🎯 Professional Examples

### 1. Full-Stack Development
```bash
# Create a new React component with TypeScript
> create components/UserProfile.tsx "Create a user profile component with props interface, state management, and error handling"

# Set up Express.js API endpoint
> create api/users.js "Create RESTful API endpoint for user management with validation and error handling"

# Run development server with streaming output
> run npm run dev
```

### 2. Python Development
```bash
# Create a data processing pipeline
> create data_pipeline.py "Create a robust data processing pipeline with pandas, error handling, and logging"

# Run tests with real-time output
> run python -m pytest tests/ -v

# Analyze project structure
> analyze
📊 Detected: Python project with pytest, pandas, numpy
🌿 Git: main branch, 3 uncommitted changes
📦 Dependencies: 15 packages, all up to date
```

### 3. DevOps & Deployment
```bash
# Create Docker configuration
> create Dockerfile "Create optimized Dockerfile for Python Flask application"

# Build and run with streaming logs
> run docker build -t myapp . && docker run -p 5000:5000 myapp

# Deploy to production
> run kubectl apply -f deployment.yaml
```

## 🔄 Session Management

The enhanced agent maintains persistent sessions:

```bash
# Sessions are automatically saved every 30 seconds
# Manual session operations:
> session save    # Save current session
> session load    # Load previous session  
> session clear   # Clear session history
> session info    # Show session details
```

## 📊 Project Intelligence

The agent provides deep project insights:

```bash
> analyze

📊 Enhanced Project Analysis:
├── 🏗️  Architecture: Microservices (Node.js + Python)
├── 📦 Dependencies: 23 packages (2 outdated)
├── 🌿 Git Status: feature/auth-system (+15 -3)
├── 🧪 Tests: 85% coverage (127/150 tests passing)
├── 📁 Structure: 45 files across 8 directories
├── 🔍 Code Quality: 8.5/10 (ESLint: 2 warnings)
└── 🚀 Deployment: Ready (Docker + K8s configs found)
```

## 🔧 Requirements

- **Python 3.8+** (enhanced async support)
- **Hugging Face API token** (or other AI provider)
- **Git** (optional, for enhanced git integration)
- **Cross-platform support**: Windows 10+, Linux, macOS

## 🎉 What's New in Enhanced Version

✅ **Cross-platform terminal support** (Windows + Unix)  
✅ **Real-time streaming command execution**  
✅ **Professional session management**  
✅ **Enhanced configuration system**  
✅ **Automatic backup and restore**  
✅ **Safe command detection and validation**  
✅ **Improved error handling and logging**  
✅ **Better project analysis and context**  
✅ **Performance optimizations**  
✅ **Enterprise-ready architecture**  

## 📄 License

MIT License - see LICENSE file for details.

---

**Ready to revolutionize your development workflow?**  
Start with: `python qwen_agent.py`
