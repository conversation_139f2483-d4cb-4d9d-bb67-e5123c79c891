@echo off
REM Enhanced Qwen Coder Agent Startup Script
REM This script starts the agent with proper configuration

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 Enhanced Qwen Coder Agent                 ║
echo ║           Professional Coding Assistant v2.0                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if HF_TOKEN is set
if "%HF_TOKEN%"=="" (
    echo ⚠️  Warning: HF_TOKEN environment variable not set
    echo 💡 Get your free token at: https://huggingface.co/settings/tokens
    echo 🔧 Set it with: set HF_TOKEN=your_token_here
    echo.
    set /p token="Enter your Hugging Face token (or press Enter to continue): "
    if not "!token!"=="" (
        python qwen_agent.py --api-key !token!
    ) else (
        python qwen_agent.py --api-key demo
    )
) else (
    echo ✅ HF_TOKEN found, starting agent...
    python qwen_agent.py
)

echo.
echo 👋 Agent session ended. Press any key to exit...
pause >nul
