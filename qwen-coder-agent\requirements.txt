# Core dependencies
requests>=2.28.0
aiohttp>=3.8.0
asyncio-mqtt>=0.11.0

# AI Provider integrations
openai>=1.0.0
anthropic>=0.7.0
google-generativeai>=0.3.0

# Terminal and process management
pexpect>=4.8.0
ptyprocess>=0.7.0

# File system operations
watchdog>=3.0.0
filelock>=3.12.0

# WebSocket support
websockets>=11.0.0

# Database
sqlite3  # Built-in with Python

# Utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
typing-extensions>=4.5.0

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Optional: Language Server Protocol support
python-lsp-server>=1.7.0
pylsp-mypy>=0.6.0

# Optional: Git integration
GitPython>=3.1.0

# Optional: Code analysis
ast-decompiler>=0.7.0
astpretty>=3.0.0

# Optional: Project scaffolding
cookiecutter>=2.1.0
jinja2>=3.1.0

# Optional: Performance monitoring
psutil>=5.9.0
memory-profiler>=0.60.0
# readline is built-in on Unix/Linux/Mac, not available on Windows
# argparse and pathlib are built-in to Python 3.7+
