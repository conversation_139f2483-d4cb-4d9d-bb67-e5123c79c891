#!/usr/bin/env python3
"""
Quick Demo of Enhanced Qwen Coder Agent
Shows the key improvements and features
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from qwen_agent import QwenCoderAgent, Colors

def main():
    """Quick demo of the enhanced agent"""
    print(f"{Colors.HEADER}🚀 Enhanced Qwen Coder Agent Demo{Colors.ENDC}")
    print("=" * 50)
    
    try:
        # Initialize the enhanced agent
        print(f"\n{Colors.OKCYAN}Initializing Enhanced Agent...{Colors.ENDC}")
        agent = QwenCoderAgent()
        
        print(f"✅ Agent initialized successfully!")
        print(f"📁 Working Directory: {agent.config.working_directory}")
        print(f"🔧 Model: {agent.config.model}")
        print(f"🛡️  Safe Mode: {agent.config.safe_mode}")
        print(f"📡 Streaming: {agent.config.streaming}")
        print(f"🗂️  Session ID: {agent.session_data.session_id}")
        
        # Test command execution
        print(f"\n{Colors.OKCYAN}Testing Command Execution...{Colors.ENDC}")
        result = agent.execute_command("echo Hello Enhanced Agent!", confirm=False, stream=True)
        print(f"✅ Command executed with exit code: {result.exit_code}")
        
        # Show session info
        print(f"\n{Colors.OKCYAN}Session Information...{Colors.ENDC}")
        print(f"📊 Commands executed: {len(agent.executed_commands)}")
        print(f"💬 Conversation history: {len(agent.session_history)} messages")
        print(f"📁 Active files: {len(agent.active_files)} files")
        
        # Show configuration
        print(f"\n{Colors.OKCYAN}Configuration...{Colors.ENDC}")
        print(f"⚙️  Auto Save: {agent.config.auto_save}")
        print(f"💾 Backup Enabled: {agent.config.backup_enabled}")
        print(f"🌿 Git Integration: {agent.config.git_integration}")
        
        print(f"\n{Colors.HEADER}🎉 Demo Complete!{Colors.ENDC}")
        print(f"{Colors.OKGREEN}The Enhanced Qwen Coder Agent is ready for use.{Colors.ENDC}")
        print(f"\n{Colors.WARNING}To start the agent interactively:{Colors.ENDC}")
        print(f"  python qwen_agent.py")
        
        return 0
        
    except Exception as e:
        print(f"{Colors.FAIL}❌ Demo failed: {e}{Colors.ENDC}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
